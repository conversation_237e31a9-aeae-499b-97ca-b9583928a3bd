﻿using Alkami.Common;
using Alkami.MicroServices.Security.Contracts.Requests;
using Alkami.Security.Common.Claims;
using Alkami.Security.Common.DataContracts;
using Common.Logging;
using ECU.Client.Widget.DirectDepositInfo.Models;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using WebToolkit;

namespace ECU.Client.Widget.DirectDepositInfo.Controllers
{
    [ClaimsAuthorizationFilter(PermissionNames.NoPermissions)]
    public class ECUDirectDepositInfoController : BaseDirectDepositInfoController
    {
        

        

        
    }
}
