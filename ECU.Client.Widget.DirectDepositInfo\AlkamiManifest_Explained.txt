This content was added to the file AlkamiManifest.xml in your folder:

--------------------------------------------------------------

<?xml version="1.0"?>
<packageManifest>
    <version>1.0</version>
    <general>
        <creatorCode>ECU</creatorCode>
        <compound>ECU_SDK</compound>
        <element>ECU.Client.Widget.DirectDepositInfo</element>
        <componentType>Widget</componentType>		<!-- SDK clients should add their BankIdentifier(s) in this list. No quotes or brackets - {} - are required. Dashes are encouraged.
        <bankIdentifiers>
            <bankIdentifier name="ECU">{Guid}</bankIdentifier>
        </bankIdentifiers>
		-->
    </general>

    <!-- Widget -->
    <widgetManifest>
        <widgetName>ECU.Client.Widget.DirectDepositInfo</widgetName>
        <!-- <widgetInstall>Client|Admin|Generic</widgetInstall> - Generic means this gets installed in both Admin and Client with v1.0 manifests -->
        <widgetInstall>Client</widgetInstall>
        <areaName>ECUDirectDepositInfo</areaName>
        <assemblyInfo>ECU.Client.Widget.DirectDepositInfo</assemblyInfo>
		<!-- <displaySettings>Desktop|Mobile|Tablet|DesktopMobile|All</displaySettings> -->
        <displaySettings>All</displaySettings>
        <iconName>REPLACE_ME</iconName>
    </widgetManifest>
</packageManifest>

--------------------------------------------------------------

This explains the options that can be configured:

    <version>1.0</version>

This is the Alkami Manifest file version. This version is not to be incremend by the implementer, this is used so we can ensure the schema format matches intent.

    <general>
        <creatorCode>ECU</creatorCode>

This value is either Alkami or the Alkami provided institution code. US Best Federal Credit Union would typically become USBFCU. Please use the Alkami provided designator.
        
        <compound>ECU_SDK</compound>

For most clients, this value is your designator + _SDK and we ask that you keep it this way. This helps our automation when it comes to installing your packages.
Internal teams should work with their POs.

        <element>ECU.Client.Widget.DirectDepositInfo</element>

The element should match your root namespace of your widget. This should also typically match your <Alkami provided designator>.<Client|Admin>.Widgets.<URL Slug> - This matches the Alkami format.

        <componentType>Widget</componentType>

The component type for all widgets is Widget. This applies to Admin as well as Client (see below)
		<!-- SDK clients should add their BankIdentifier(s) in this list. No quotes or brackets - {} - are required. Dashes are encouraged.
        <bankIdentifiers>
            <bankIdentifier name="ECU">{Guid}</bankIdentifier>
        </bankIdentifiers>
		-->

If you do not know this information, you can reach out to the SDK Support team. If you have a locally stage-matched database, it will be in AlkamiMaster.dbo.Tenant.

    </general>

    <!-- Widget -->
    <widgetManifest>
        <widgetName>ECU.Client.Widget.DirectDepositInfo</widgetName>

This should match the name of your nuspec file. This is used in conjunction with the compound element above. For SDK clients, this is used for automation.

        <!-- <widgetInstall>Client|Admin|Generic</widgetInstall> - Generic means this gets installed in both Admin and Client with v1.0 manifests -->
        <widgetInstall>Client</widgetInstall>

If you would like your widget installed in WebClient or Admin only, set this to the appropriate value of Client or Admin. 
If you would like it installed in both areas (few widgets are written for both areas), use Generic. 
When in dobut, please use Client.

        <areaName>ECUDirectDepositInfo</areaName>

This should be the expected URL slug of your widget. Example: http://developer.dev.alkamitech.com/Authentication - the widget areaName is Authentication. This should match the end of your namespace.

        <assemblyInfo>ECU.Client.Widget.DirectDepositInfo</assemblyInfo>

This should match your nuspec name and your root namespace.

		<!-- <displaySettings>Desktop|Mobile|Tablet|DesktopMobile|All</displaySettings> -->
        <displaySettings>All</displaySettings>

This value dictates what devices your widget will be shown on. Most implementations prefer "All".

        <iconName>REPLACE_ME</iconName>

This value should be replaced with the appropriate value.

    </widgetManifest>
</packageManifest>


When being installed via automation, the values in this will typically override what is in the database, except the AreaName and the AssemblyInfo objects. Those should only be updated when contacting Alkami Support for production/staging updates.

