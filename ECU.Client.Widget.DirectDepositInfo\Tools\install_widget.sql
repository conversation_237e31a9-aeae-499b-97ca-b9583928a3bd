﻿-- This script is only used for local development
USE [DeveloperDynamic]
GO

/* Variable Declarations. Altering this line will prevent your script from being run. */ SET XACT_ABORT ON;BEGIN TRAN; DECLARE @InsertedDisplayName BIT = 0,@InsertedDescription BIT = 0,@widgetSettingValuesBefore VARCHAR(MAX),@widgetSettingValuesAfter VARCHAR(MAX), @WidgetID BIGINT = -1,@WidgetName NVARCHAR(100) = NULL,@WidgetAssemblyInfo NVARCHAR(255) = NULL,@WidgetIconImage varbinary(max) = NULL,@WidgetIconContentType varchar(100) = NULL,@Assembly nvarchar(300) = NULL,@WidgetDisplayName nvarchar(500) = NULL,@WidgetDescription nvarchar(max) = NULL,@WidgetDisplaySettings INT = NULL,@WidgetNativeDisplaySetting INT = NULL,@WidgetCanBeRemoved bit = NULL,@WidgetActive bit = NULL,@WidgetSalt nvarchar(255) = NULL,@WidgetAddedByDefault bit = NULL,@WidgetFavedByDefault bit = NULL,@WidgetOrdering INT = NULL,@WidgetIsDefault bit = NULL,@WidgetType INT = NULL,@WidgetIconName NVARCHAR(50) = NULL,@Environment INT = 1,@DevEnvironment INT = 1,@StagingEnvironment INT = 2,@ProductionEnvironment INT = 3,@WidgetInserted BIT = 0,@WidgetUpdated BIT = 0,@BankIdentifier UniqueIdentifier,@AuditSummary NVARCHAR(100) = '',@AuditDescription NVARCHAR(MAX) = '',@AuditType INT = 0;select @BankIdentifier = BankIdentifier from core.Bank;DECLARE @WidgetSettings TABLE (Name NVARCHAR(255), Value NVARCHAR(MAX));SET NOCOUNT ON; SELECT @Environment = @DevEnvironment;

SELECT   @WidgetName = 'ECUDirectDepositInfo' --required
		,@WidgetAssemblyInfo = 'ECU.Client.Widget.DirectDepositInfo' --required
		,@WidgetDisplayName = 'Direct Deposit Letter' -- User friendly display name
		,@WidgetDescription = 'Generates direct deposit letter' -- User friendly description
		,@WidgetIconName = 'pdf'
		,@WidgetDisplaySettings = 7
		--,@WidgetNativeDisplaySetting = 15

/* WIDGET SETTINGS: If you don't have any WidgetSettings then feel free to comment this whole block out. */
-- INSERT INTO @WidgetSettings (Name, Value) 
-- VALUES ('Name 1 goes here', 'Setting value 1 goes here')
--	 , ('Name 2 goes here', 'Setting value 2 goes here')
-- for each widget setting after the first, copy and paste the above row multiple times.
-- uncomment each row that you insert in this manner.

/* DO NOT EDIT BELOW THIS LINE: The following lines have been compressed to save space and enhance focus on the parts that matter. Thank you for understanding. */
SET NOCOUNT OFF; IF @WidgetName IS NULL OR @WidgetName = '' OR @WidgetAssemblyInfo IS NULL OR @WidgetAssemblyInfo = '' BEGIN RAISERROR ('Widget Name and AssemblyInfo can not be null',16,1);RETURN; END IF NOT EXISTS (SELECT * FROM core.Widget WHERE AssemblyInfo = @WidgetAssemblyInfo) BEGIN PRINT 'Inserting into core.Widget';INSERT INTO core.Widget (BankID,Name,AssemblyInfo,Salt,Active,IconImage,IconContentType,CanBeRemoved,Slug,CreateDate,DisplaySettings,WidgetType,IconName) SELECT ID, @WidgetName,@WidgetAssemblyInfo,ISNULL(@WidgetSalt,''),ISNULL(@WidgetActive,1),@WidgetIconImage,ISNULL(@WidgetIconContentType,''),ISNULL(@WidgetCanBeRemoved,1),NULL,GETUTCDATE(),ISNULL(@WidgetDisplaySettings,1),ISNULL(@WidgetType,1),ISNULL(@WidgetIconName,'') from core.Bank; SELECT @WidgetInserted = 1;SELECT @AuditSummary = N'Inserted new widget for ' + @WidgetAssemblyInfo, @AuditDescription = N'Inserted new widget for ' + @WidgetAssemblyInfo, @AuditType = 0;SET NOCOUNT ON; INSERT INTO audit.UserAction(BankIdentifier,Area,EntityName,EntityID,UserReference,DisplayName,IPAddress,Summary,Description,ClientName,CreateDate,AdminReference,AdminDisplayName,AdminIPAddress,AuditEvent,UserSessionId,Status)VALUES(@BankIdentifier,'Widget Installer',NULL,NULL,@BankIdentifier,'Service Admin','127.0.0.1',@AuditSummary,@AuditDescription,'Widget Installer',GETUTCDATE(),@BankIdentifier,'Service Admin','127.0.0.1',@AuditType,'0000000000000000000000',1);SET NOCOUNT OFF;END SELECT @WidgetId = ID FROM core.Widget WHERE AssemblyInfo = @WidgetAssemblyInfo; IF @Environment = @DevEnvironment AND @WidgetInserted = 0 BEGIN PRINT 'Updating into core.Widget';SELECT @widgetSettingValuesBefore = N'Updated Widget table values for ' + @WidgetAssemblyInfo + N'values Name [' + Name + N':' + @WidgetName+ N'],Salt [' + ISNULL(Salt,'<NULL>') + N':' + ISNULL(@WidgetSalt,'<NULL>')+ N'],Active[ ' + CONVERT(NVARCHAR(MAX),ISNULL(Active,1)) + N':' + CONVERT(NVARCHAR(MAX),ISNULL(@WidgetActive,1))+ N'],IconImage [<omitted>:' +ISNULL(CONVERT(NVARCHAR(MAX),@WidgetIconImage,0),'<null>')+ N'],IconContentType [' + ISNULL(IconContentType,'<NULL>') + N':' + ISNULL(@WidgetIconContentType,'<NULL>')+ N'],CanBeRemoved [' + CONVERT(NVARCHAR(MAX),ISNULL(CanBeRemoved,1)) + N':' + CONVERT(NVARCHAR(MAX),ISNULL(@WidgetCanBeRemoved,1))+ N'],DisplaySettings [' + CONVERT(NVARCHAR(MAX),ISNULL(DisplaySettings,1)) + N':' + CONVERT(NVARCHAR(MAX),ISNULL(@WidgetDisplaySettings,1))+ N'],WidgetType [' + CONVERT(NVARCHAR(MAX),ISNULL(WidgetType,1)) + N':' + CONVERT(NVARCHAR(MAX),ISNULL(@WidgetType,1))+ N'],IconName [' + ISNULL(IconName,'<NULL>') + N':' + ISNULL(@WidgetIconName,'<NULL>') + N']' FROM core.Widget WHERE AssemblyInfo = @WidgetAssemblyInfo;UPDATE core.Widget SET Name = @WidgetName,Salt = ISNULL(@WidgetSalt,''),Active = ISNULL(@WidgetActive,1),IconImage = @WidgetIconImage,IconContentType = ISNULL(@WidgetIconContentType,''),CanBeRemoved = ISNULL(@WidgetCanBeRemoved,1),DisplaySettings = ISNULL(@WidgetDisplaySettings,1),WidgetType = ISNULL(@WidgetType,1),IconName = ISNULL(@WidgetIconName,'') WHERE AssemblyInfo = @WidgetAssemblyInfo;SELECT @WidgetUpdated = 1;SELECT @AuditSummary = 'Updated core.Widget for ' + CONVERT(NVARCHAR(MAX),@WidgetID), @AuditDescription =  ISNULL(@widgetSettingValuesBefore,''), @AuditType = 0;SET NOCOUNT ON; INSERT INTO audit.UserAction(BankIdentifier,Area,EntityName,EntityID,UserReference,DisplayName,IPAddress,Summary,Description,ClientName,CreateDate,AdminReference,AdminDisplayName,AdminIPAddress,AuditEvent,UserSessionId,Status)VALUES(@BankIdentifier,'Widget Installer',NULL,NULL,@BankIdentifier,'Service Admin','127.0.0.1',@AuditSummary,@AuditDescription,'Widget Installer',GETUTCDATE(),@BankIdentifier,'Service Admin','127.0.0.1',@AuditType,'0000000000000000000000',1);SET NOCOUNT OFF;END PRINT 'Inserting widgetsettings'; INSERT INTO core.WidgetSetting SELECT @WidgetID, ws.Name, ws.Value,GETUTCDATE() FROM @WidgetSettings ws LEFT JOIN core.WidgetSetting s ON s.WidgetID = @WidgetID AND s.Name = ws.Name WHERE s.ID IS NULL;SELECT @widgetSettingValuesBefore = COALESCE(@widgetSettingValuesBefore + ', ', '') + N'(Name:[' + Name + N'],Value:[' + Value + N'])'FROM @WidgetSettings;SELECT @AuditSummary = 'Inserted WidgetSettings', @AuditDescription = N'Inserted WidgetSettings ' + ISNULL(@widgetSettingValuesBefore,''), @AuditType = 0;SET NOCOUNT ON; INSERT INTO audit.UserAction(BankIdentifier,Area,EntityName,EntityID,UserReference,DisplayName,IPAddress,Summary,Description,ClientName,CreateDate,AdminReference,AdminDisplayName,AdminIPAddress,AuditEvent,UserSessionId,Status)VALUES(@BankIdentifier,'Widget Installer',NULL,NULL,@BankIdentifier,'Service Admin','127.0.0.1',@AuditSummary,@AuditDescription,'Widget Installer',GETUTCDATE(),@BankIdentifier,'Service Admin','127.0.0.1',@AuditType,'0000000000000000000000',1);SET NOCOUNT OFF; IF @Environment = @DevEnvironment BEGIN SELECT @widgetSettingValuesBefore = COALESCE(@widgetSettingValuesBefore + ', ', '') + N'(Name:[' + Name + N'],Value:[' + Value + N'])'FROM core.WidgetSetting WHERE WidgetID = @WidgetID;PRINT 'Updating WidgetSettings';UPDATE s SET Value = ws.Value FROM @WidgetSettings ws JOIN core.WidgetSetting s ON s.WidgetID = @WidgetID AND s.Name = ws.Name;SELECT @widgetSettingValuesAfter = COALESCE(@widgetSettingValuesAfter + ', ', '') + N'(Name:[' + Name + N'],Value:[' + Value + N'])'FROM core.WidgetSetting WHERE WidgetID = @WidgetID;SELECT @AuditSummary = 'Updated widgetsettings', @AuditDescription = 'Updated WidgetSettings from: ' + ISNULL(@widgetSettingValuesBefore,'') + N' to ' + ISNULL(@widgetSettingValuesAfter,''), @AuditType = 0;SET NOCOUNT ON;INSERT INTO audit.UserAction(BankIdentifier,Area,EntityName,EntityID,UserReference,DisplayName,IPAddress,Summary,Description,ClientName,CreateDate,AdminReference,AdminDisplayName,AdminIPAddress,AuditEvent,UserSessionId,Status)VALUES(@BankIdentifier,'Widget Installer',NULL,NULL,@BankIdentifier,'Service Admin','127.0.0.1',@AuditSummary,@AuditDescription,'Widget Installer',GETUTCDATE(),@BankIdentifier,'Service Admin','127.0.0.1',@AuditType,'0000000000000000000000',1);SET NOCOUNT OFF; END ELSE BEGIN IF EXISTS (SELECT * FROM core.WidgetSetting ws JOIN @WidgetSettings t ON t.Name = ws.Name AND ws.WidgetID = @WidgetID WHERE t.Value <> ws.Value) BEGIN SELECT @widgetSettingValuesBefore = COALESCE(@widgetSettingValuesBefore + ', ', '') + N'(Name:[' + ws.Name + N'],Old Value:[' + ws.Value + N'],New Value['+t.Value+N'])' FROM core.WidgetSetting ws JOIN @WidgetSettings t ON t.Name = ws.Name AND ws.WidgetID = @WidgetID WHERE t.Value <> ws.Value; PRINT 'WARNING!'; PRINT 'WARNING!'; PRINT 'Please tell TI or Support that the widgetsettings below were altered so they can be manually updated!';PRINT 'WARNING!'; PRINT 'WARNING!';PRINT '';PRINT @widgetSettingValuesBefore; END END SELECT @WidgetOrdering=ISNULL(MIN(fw.Ordering),ISNULL(@WidgetOrdering,999))-1from core.widget w join core.FlavorWidget fw on fw.WidgetID = w.ID where w.name = 'Settings';
IF(@Environment = @DevEnvironment OR @Environment = @StagingEnvironment) AND @WidgetInserted = 1 BEGIN PRINT 'Inserting FlavorWidget'; INSERT INTO core.FlavorWidget(FlavorID,WidgetID,AddedByDefault,FavedByDefault,CreateDate,Ordering,IsDefault,DisplaySetting,NativeDisplaySetting)SELECT f.ID,@WidgetID,ISNULL(@WidgetAddedByDefault,1),ISNULL(@WidgetFavedByDefault,0),GETUTCDATE(),@WidgetOrdering,ISNULL(@WidgetIsDefault,0),ISNULL(@WidgetDisplaySettings,1),ISNULL(@WidgetNativeDisplaySetting,0) FROM core.Flavor f;SELECT @AuditSummary = N'Added widget '+@WidgetAssemblyInfo+N' to FlavorWidget table for all flavors', @AuditDescription = N'Added widget '+@WidgetAssemblyInfo+N' to FlavorWidget table for all flavors', @AuditType = 7; SET NOCOUNT ON; INSERT INTO audit.UserAction(BankIdentifier,Area,EntityName,EntityID,UserReference,DisplayName,IPAddress,Summary,Description,ClientName,CreateDate,AdminReference,AdminDisplayName,AdminIPAddress,AuditEvent,UserSessionId,Status)VALUES(@BankIdentifier,'Widget Installer',NULL,NULL,@BankIdentifier,'Service Admin','127.0.0.1',@AuditSummary,@AuditDescription,'Widget Installer',GETUTCDATE(),@BankIdentifier,'Service Admin','127.0.0.1',@AuditType,'0000000000000000000000',1); SET NOCOUNT OFF; END; IF (@WidgetInserted = 1 AND @Environment = @StagingEnvironment) OR @Environment = @DevEnvironment BEGIN PRINT 'Inserting into UserWidget table'; INSERT INTO core.UserWidget (UserID,WidgetID,IsFavorite,Ordering,DeactivationDate,CreateDate,IsMobileFavorite,MobileOrdering)SELECT u.ID, @WidgetID, ISNULL(@WidgetFavedByDefault,0),@WidgetOrdering,NULL,GETUTCDATE(),0,0 FROM core.Users u LEFT JOIN core.UserWidget uw on uw.UserID = u.ID and uw.WidgetID = @WidgetID WHERE uw.ID IS NULL;SELECT @AuditSummary = N'Added widget '+@WidgetAssemblyInfo+N' to UserWidget table for all users', @AuditDescription = N'Added widget '+@WidgetAssemblyInfo+N' to UserWidget table for all users', @AuditType = 8;SET NOCOUNT ON;INSERT INTO audit.UserAction(BankIdentifier,Area,EntityName,EntityID,UserReference,DisplayName,IPAddress,Summary,Description,ClientName,CreateDate,AdminReference,AdminDisplayName,AdminIPAddress,AuditEvent,UserSessionId,Status)VALUES(@BankIdentifier,'Widget Installer',NULL,NULL,@BankIdentifier,'Service Admin','127.0.0.1',@AuditSummary,@AuditDescription,'Widget Installer',GETUTCDATE(),@BankIdentifier,'Service Admin','127.0.0.1',@AuditType,'0000000000000000000000',1); SET NOCOUNT OFF;END;SELECT @WidgetName = Name FROM core.Widget WHERE AssemblyInfo = @WidgetAssemblyInfo; IF NOT EXISTS (SELECT * FROM dbo.LocalizableResource WHERE Name = 'Widget.' + @WidgetName) BEGIN PRINT 'Insert into LocalizableResource'; INSERT INTO dbo.LocalizableResource (Name, SecondaryId) SELECT 'Widget.' + @WidgetName, -1; END IF NOT EXISTS (SELECT * FROM core.Item where ParentID = (SELECT TOP 1 ID FROM dbo.LocalizableResource WHERE Name = 'Widget.' + @WidgetName) AND ItemType = 'Localizable Resource') BEGIN PRINT 'Insert Localizable Resource record into core.Item'; INSERT INTO core.Item (ItemType,ParentId,SecondaryId,Name,CreatedUtc,Version,Deleted,LastUpdate) SELECT 'Localizable Resource', (SELECT TOP 1 ID FROM dbo.LocalizableResource WHERE Name = 'Widget.' + @WidgetName),1033,'Widget.' + @WidgetName,GETUTCDATE(),'*******',0,NULL; SET NOCOUNT ON; INSERT INTO core.Changeset SELECT @@IDENTITY,100000000000000,'Service Admin',GETUTCDATE(),'Added a Widget LocalizableResource'; SET NOCOUNT OFF; END DECLARE @ItemId BIGINT = (SELECT TOP 1 ID FROM core.Item where ParentID = (SELECT TOP 1 ID FROM dbo.LocalizableResource WHERE Name = 'Widget.' + @WidgetName) AND ItemType = 'Localizable Resource');IF @WidgetDisplayName IS NOT NULL AND @WidgetDisplayName != '' BEGIN IF NOT EXISTS (SELECT * FROM core.ItemSetting WHERE ItemID = @ItemId AND Name = 'DisplayName') BEGIN PRINT 'Insert itemsetting for DisplayName'; INSERT INTO core.ItemSetting (ItemId,Name,Value,CreatedUtc,Version,LastUpdate)SELECT @ItemId,'DisplayName',ISNULL(@WidgetDisplayName,@WidgetName),GETUTCDATE(),'*******',NULL;SET NOCOUNT ON;INSERT INTO core.Changeset SELECT @ItemID,100000000000000,'Service Admin',GETUTCDATE(),'Added a Widget DisplayName';INSERT INTO core.ChangesetDetail (ChangesetId,ItemSettingId,OldValue,NewValue) SELECT @@IDENTITY,(SELECT TOP 1 ID FROM core.ItemSetting WHERE ItemID = @ItemID and Name = 'DisplayName'),NULL,@WidgetDisplayName;SELECT @InsertedDisplayName = 1;SET NOCOUNT OFF; END IF @Environment = @DevEnvironment AND @InsertedDisplayName = 0 BEGIN UPDATE core.ItemSetting SET Value = @WidgetDisplayName WHERE ItemID = @ItemId AND Name = 'DisplayName';END ELSE BEGIN IF EXISTS (SELECT * FROM core.ItemSetting WHERE ItemID = @ItemId AND Name = 'DisplayName' AND Value <> @WidgetDisplayName) BEGIN PRINT 'WARNING!'; PRINT 'WARNING!'; PRINT 'Please tell TI or Support that the Widget DisplayName was altered so they can manually update it!';PRINT 'WARNING!'; PRINT 'WARNING!';PRINT '';PRINT @WidgetDisplayName; END END END IF @WidgetDescription IS NOT NULL AND @WidgetDescription != '' BEGIN IF NOT EXISTS (SELECT * FROM core.ItemSetting WHERE ItemID = @ItemId AND Name = 'Description') BEGIN PRINT 'Insert ItemSetting for Description'; INSERT INTO core.ItemSetting (ItemId,Name,Value,CreatedUtc,Version,LastUpdate)SELECT @ItemId,'Description',ISNULL(@WidgetDescription,@WidgetName),GETUTCDATE(),'*******',NULL;SET NOCOUNT ON;INSERT INTO core.Changeset SELECT @ItemID,100000000000000,'Service Admin',GETUTCDATE(),'Added a Widget Description';INSERT INTO core.ChangesetDetail (ChangesetId,ItemSettingId,OldValue,NewValue) SELECT @@IDENTITY,(SELECT TOP 1 ID FROM core.ItemSetting WHERE ItemID = @ItemID and Name = 'Description'),NULL,@WidgetDescription;SET NOCOUNT OFF;SELECT @InsertedDescription = 1;END IF @Environment = @DevEnvironment AND @InsertedDescription = 0 BEGIN UPDATE core.ItemSetting SET Value = @WidgetDisplayName WHERE ItemID = @ItemId AND Name = 'Description';END ELSE BEGIN IF EXISTS (SELECT * FROM core.ItemSetting WHERE ItemID = @ItemId AND Name = 'Description' AND Value <> @WidgetDescription) BEGIN PRINT 'WARNING!'; PRINT 'WARNING!'; PRINT 'Please tell TI or Support that the Widget Description was altered so they can manually update it!';PRINT 'WARNING!'; PRINT 'WARNING!';PRINT '';PRINT @WidgetDescription; END END END
/* DO NOT EDIT ABOVE THIS LINE: The preceding lines have been compressed to save space and enhance focus on the parts that matter. Thank you for understanding. */

/* TRANSACTION: Always track and submit this file with ROLLBACK turned on*/
ROLLBACK TRAN
--COMMIT TRAN