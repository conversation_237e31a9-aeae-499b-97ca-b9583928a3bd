using Alkami.App.Core.Data.Entities;
using Alkami.Client.Framework.Mvc;
using Alkami.Client.Services.Bank.Repository;
using Alkami.Common;
using Alkami.MicroServices.Accounts.Contracts;
using Alkami.MicroServices.Accounts.Contracts.Requests;
using Alkami.MicroServices.Accounts.Contracts.Responses;
using Alkami.MicroServices.Accounts.Contracts.Sorters;
using Alkami.MicroServices.Accounts.Data;
using Alkami.MicroServices.Accounts.Service.Client;
using Alkami.MicroServices.Security.Contracts;
using Alkami.MicroServices.Security.Contracts.Requests;
using Alkami.MicroServices.Security.Data;
using Alkami.MicroServices.Security.Service.Client;
using Alkami.MicroServices.Settings.Contracts;
using Alkami.MicroServices.Settings.Contracts.Filters_And_Mappers;
using Alkami.MicroServices.Settings.Contracts.Requests;
using Alkami.MicroServices.Settings.Data;
using Alkami.Security.Common.Claims;
using Alkami.Security.Common.DataContracts;
using Alkami.MicroServices.SymConnectMultiplexer.Service.Client;
using Common.Logging;
using ECU.Client.Widget.DirectDepositInfo.Models;
using ECU.Client.Widget.DirectDepositInfo.Utils;
using HiQPdf;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using WebToolkit;
using Alkami.MicroServices.SymConnectMultiplexer.Contracts;
using Alkami.MicroServices.SymConnectMultiplexer.Utilities;
using Alkami.MicroServices.SymConnectMultiplexer.Contracts.Requests;
using Alkami.MicroServices.SymConnectMultiplexer.Contracts.Responses;
using NHibernate.Cfg;




namespace ECU.Client.Widget.DirectDepositInfo.Controllers
{
    [ClaimsAuthorizationFilter(PermissionNames.NoPermissions)]
    public class BaseDirectDepositInfoController : BaseController
    {
        /// <summary>
        /// Gets the logger
        /// </summary>
        private static readonly ILog Logger = LogManager.GetLogger<ECUDirectDepositInfoController>();

        public ICustomerRepository CustomerRepository { get; set; }

        protected Dictionary<long, string> accountDisplayNames = new Dictionary<long, string>();
        protected Dictionary<long, int> accountThemeColoIndex = new Dictionary<long, int>();
        protected Dictionary<long, int> accountRelationships = new Dictionary<long, int>();

        public static Func<IAccountServiceContract> accountsService = () => new AccountServiceClient();
        public static Func<ISecurityServiceContract> securityService = () => new SecurityServiceClient();
        public static Func<ISettingsServiceContract> settingsService = () => new Alkami.MicroServices.Settings.Service.Client.ServiceClient();
        public static Func<IMultiplexerContract> poweronService = () => new MultiplexerClient();

        public async Task<ActionResult> Index()
        {
            Logger.Trace("[GET] DirectDepositInfoController/Index");
            var accountsResponse = await GetAccounts();
            var accountsForDropdown = new List<AccountInfoModel>();

            

            if (accountsResponse.Accounts != null && accountsResponse.Accounts.Count > 0)
            {

                var MICRAccounts = from accounts in accountsResponse.Accounts
                                   where !string.IsNullOrEmpty(accounts.MICRAccountNumber)
                                   && (accounts.AccountType.AccountTypeIsExtensible & AccountTypeIsExtensible.AssetAccount) == AccountTypeIsExtensible.AssetAccount
                                   && (accounts.AccountType.AccountTypeIsExtensible & AccountTypeIsExtensible.CertificateAccount) != AccountTypeIsExtensible.CertificateAccount
                                   && (accounts.AccountType.AccountTypeIsExtensible & AccountTypeIsExtensible.HSAAccount) != AccountTypeIsExtensible.HSAAccount
                                   && (accounts.AccountType.AccountTypeAllows2 & AccountTypeAllows2.TransfersIn) == AccountTypeAllows2.TransfersIn
                                   select accounts;

                foreach (var account in MICRAccounts)
                {

                    Logger.DebugFormat("Populating account: {0}", account.MaskedAccountNumber?.AccountNumber);
                    var newAccount = new AccountInfoModel()
                    {
                        DisplayName = accountDisplayNames[account.Id],
                        AccountType = account.AccountType.AccountTypeIsExtensible.ToString(),
                        AccountNumber = account.MaskedAccountNumber?.AccountNumber,
                        AccountBalance = account.AvailableBalance,
                        ThemeColorIndex = accountThemeColoIndex[account.Id],
                        AccountIdentifier = account.AccountIdentifier.ToString(),
                        IsLinkedAccount = accountRelationships[account.Id] == (int)Alkami.Common.RelationshipType.Linked ? true : false
                    };
                    accountsForDropdown.Add(newAccount);
                }

                //}
                //}
            }
            else
            {
                Logger.Error("No accounts returned from Accounts microservice.");
                return View("Error");
            }

            var model = new DirectDepositInfoModel
            {
                Accounts = accountsForDropdown,
                FirstName = CurrentUser.FirstName,
                LastName = CurrentUser.LastName
            };

            return View(model);
        }

        [HttpGet]
        public async Task<FileResult> GetPdf(Guid documentId)
        {
            Logger.TraceFormat("Entering DirectDepositController/GetPdf(documentId={0}", documentId);
            byte[] pdf = await CreatePdf(documentId);
            return File(pdf, "application/pdf");
        }

        protected async Task<Item> GetBankSettings()
        {
            var getItemRequest = new GetItemRequest()
            {
                Filter = new ItemFilter()
                {
                    ItemType = "BankSettings"
                },
                Mapping = new ItemMapper()
                {
                    ShouldIncludeSettings = true
                }
            };

            this.AugmentRequest(getItemRequest);

            var bankSettings = await settingsService().GetItemsAsync(getItemRequest);

            return bankSettings.Items.FirstOrDefault(item => item.ItemType == "BankSettings");
        }

        protected async Task<AccountResponse> GetAccounts()
        {
            Logger.Trace("Entering DirectDepositController/GetAccouts method");
            var member = CustomerRepository.Get();
            Logger.TraceFormat("CustomerRepository User retrieved with ID: {0}", member.Model.ID);

            var memberRequest = new GetUserRequest()
            {
                UserId = member.Model.ID,
                Mapping = new UserMapper()
                {
                    ShouldIncludeExternalUserAccounts = false,
                    ShouldIncludeUserAccounts = true,
                    ShouldIncludePermissions = true,
                    ShouldIncludeUserAccountPermissions = true
                },
                MaxResults = 1
            };

            this.AugmentRequest(memberRequest);

            Logger.Trace("Calling Security microservice");
            var userResponse = await securityService().GetUserAsync(memberRequest);

            if (userResponse.Users == null || userResponse.Users.Count == 0)
            {
                throw new Exception("User response was empty");
            }

            accountDisplayNames = new Dictionary<long, string>();
            List<long> accountIds = new List<long>();
            foreach (User u in userResponse.Users)
            {
                Logger.TraceFormat("Found user with User.Id: {0}", u.Id);
                foreach (UserAccount ua in u.UserAccounts)
                {
                    if (ua.AddedByCore && !ua.HideFromEndUser && !ua.Deleted && (ua.Relationship <= 3 || ua.HasMasterRights))
                    {
                        Logger.TraceFormat("Populating user account with UserAccount.AccountId: {0}", ua.AccountId);
                        accountIds.Add(ua.AccountId);
                        accountDisplayNames.Add(ua.AccountId, ua.DisplayName);
                        accountThemeColoIndex.Add(ua.AccountId, (int)ua.ThemeColorIndex);
                        accountRelationships.Add(ua.AccountId, ua.Relationship);
                    }
                }
            }


            Item bankSettings = await GetBankSettings();

            var formatString = BankSettings.GetSettingOrDefault(BankSettingName.JoinAccountHolderNumberFormatString, "{0}{1}");
            var formatOnlyAccountHolder = BankSettings.GetSettingOrDefault(BankSettingName.FormatOnlyAccountHolder, false);

            var accountRequest = new GetAccountRequest()
            {
                Filter = new AccountFilter()
                {
                    Ids = accountIds
                },
                Mapping = new AccountMapper()
                {
                    IncludeAccountType = true,
                    IncludeAccountTypeFields = null,
                    IncludeRoutingInfo = true,
                    AccountMaskSettings = new Alkami.MicroServices.Accounts.Contracts.Filters_And_Mappers.AccountMaskSettings()
                    {
                        JoinAccountHolderNumberFormatString = formatString,
                        FormatOnlyAccountHolder = formatOnlyAccountHolder,
                        PadOutput = false
                    }
                },
                Sorter = new AccountSorter()
                {
                    Ascending = true
                }
            };

            this.AugmentRequest(accountRequest);

            Logger.Trace("Calling Accounts microservice");
            var accountResponse = await accountsService().GetAccountAsync(accountRequest);
            //var accountsResponse = AsyncHelper.RunSync(() => accountsService().GetAccountAsync(accountRequest));
            Logger.Trace("Leaving DirectDepositController/GetAccouts method");
            return accountResponse;
        }

        protected async Task<Alkami.MicroServices.Accounts.Data.Account> GetAccount(Guid accountIdentifier)
        {
            Logger.TraceFormat("Entering GetAccount(accountIdentifier={0}", accountIdentifier.ToString());
            List<Guid> accountIdentifiers = new List<Guid>();
            accountIdentifiers.Add(accountIdentifier);

            var accountRequest = new GetAccountRequest() { Filter = new AccountFilter() { AccountIdentifiers = accountIdentifiers }, Mapping = new AccountMapper() { IncludeAccountType = true } };
            this.AugmentRequest(accountRequest);

            AccountResponse accountResponse = await accountsService().GetAccountAsync(accountRequest);
            if (accountResponse.Accounts == null || accountResponse.Accounts.Count == 0)
            {
                throw new Exception("Account response was empty");
            }
            Logger.Trace("Leaving GetAccount");
            return accountResponse.Accounts[0];
        }

        protected async Task<byte[]> CreatePdf(Guid accountIdentifier)
        {
            Logger.TraceFormat("Entering CreatePdf(accountIdentifier={0}", accountIdentifier);
            HtmlToPdf htmlToPdf = new HtmlToPdf();
            htmlToPdf.SerialNumber = UserWidgetSettings.HiQPDFSerial();
            var phoneNumber = UserWidgetSettings.PhoneNumber();
            var ecuAddress = UserWidgetSettings.ECUAddress();
            var createAccountNote = UserWidgetSettings.CreateAccountNote().ToLower();
            SetFooter(htmlToPdf.Document,ecuAddress,phoneNumber);

            if (htmlToPdf.SerialNumber.IsNullOrEmpty())
            {
                throw new Exception("HiQPDF serial number is missing, check widget settings."); // Ensure serial number is set
            }

            if (phoneNumber.IsNullOrEmpty())
            {
                throw new Exception("No phone number found in widget settings");
            }

            if (ecuAddress.IsNullOrEmpty())
            {
                throw new Exception("No ECU address found in widget settings");
            }

            string depPath = Path.Combine(System.Web.HttpContext.Current.Server.MapPath(@"~/Areas/ECUDirectDepositInfo/bin"), "HiQPdf.dep");
            //string depPath = @"C:\Orb\WebClient\Areas\ECUDirectDepositInfo\bin\HiQPdf.dep";
            htmlToPdf.SetDepFilePath(depPath);

            // set PDF page margins
            htmlToPdf.Document.Margins = new PdfMargins(72, 72, 48, 0);

            

            

            var account = await GetAccount(accountIdentifier);
            var addresses = CurrentUser.GetUserContact<UserContactAddress>();
            var address = addresses.FirstOrDefault();
            if (address is null)
            {
                throw new Exception("User Address not found");
            }

            var printPdfModel = new PrintPdfModel
            {
                AccountType = account.AccountType.AccountTypeIsExtensible.HasFlag(AccountTypeIsExtensible.DemandDepositAccount) ? "Checking" : "Savings",
                MICR = account.MICRAccountNumber,
                Name = CurrentUser.Name,
                AddressLine1 = address.AddressLine1,
                AddressLine2 = address.AddressLine2,
                CityStateZip = $"{address.City}, {address.State} {address.PostalCode}",
                //PDFHeaderLogo = Path.Combine(System.Web.HttpContext.Current.Server.MapPath(@"~/Areas/ECUDirectDepositInfo/Images"), "eculogo.jpg"),
                //CheckBackgroundImage = Path.Combine(System.Web.HttpContext.Current.Server.MapPath(@"~/Areas/ECUDirectDepositInfo/Images"), "ecumoegray.jpg"),
                //CheckECULogo = Path.Combine(System.Web.HttpContext.Current.Server.MapPath(@"~/Areas/ECUDirectDepositInfo/Images"), "ECU_Standard-Black White-RGB.png"),
                PDFHeaderLogo = "images/ECUMarkClear.png",
                //PDFHeaderLogo = "images/eculogo.jpg",
                CheckBackgroundImage = "images/ecumoegray.jpg",
                CheckECULogo = "images/ECU_Standard-Black White-RGB.png",
                PhoneNumber = phoneNumber,
                ECUAddress = ecuAddress
            };
            if(createAccountNote == "true")
            {
                DirectDepositNotes(account.MICRAccountNumber, account.Id, account.AccountHolder.ToString());
            }

            string baseUrl = System.Web.HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority) +
                     System.Web.VirtualPathUtility.ToAbsolute("~/Areas/ECUDirectDepositInfo/");

            
            var viewString = RenderRazorViewToString("PrintPdf", printPdfModel);
            byte[] pdfBuffer = Array.Empty<byte>();
            try
            {
                 pdfBuffer = htmlToPdf.ConvertHtmlToMemory(viewString, baseUrl);
            }
            catch (Exception ex)
            {
                Logger.Trace(ex.Message);
            }
            

            Logger.Trace("Leaving CreatePdf");
            return pdfBuffer;
        }

        private async void DirectDepositNotes(string micr, long id, string accountHolder)
        {
            var powerOnName = UserWidgetSettings.CreateNotePoweron();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendField("JRGUSERCHR1", id);
            stringBuilder.AppendField("JRGUSERCHR2", "Direct Deposit Form created for");
            stringBuilder.AppendField("JRGUSERCHR3", "MICR: " + micr);

            var body = stringBuilder.ToString();

            var repgenRequest = new CallRepGenRequest
            {
                MaxResults = 1,
                MemberIdentifier = accountHolder,
                RepGenName = powerOnName,
                Body = body
            };

            this.AugmentRequest(repgenRequest);

            Logger.DebugFormat("Calling SymConnectMultiplexer microservice with request: {0}", repgenRequest);
            CallSymConnectResponse response = await poweronService().CallRepGenAsync(repgenRequest);
            Logger.DebugFormat("SymConnectMultiplexer response: {0}", response);

        }

        public string RenderRazorViewToString(string viewName, object model)
        {
            Logger.TraceFormat("Entering RenderRazorViewToString(viewName={0}, model.Type={1})", viewName, model.GetType());
            ViewData.Model = model;
            using (var sw = new StringWriter())
            {
                var viewResult = ViewEngines.Engines.FindPartialView(ControllerContext,
                                                                         viewName);
                var viewContext = new ViewContext(ControllerContext, viewResult.View,
                                             ViewData, TempData, sw);
                viewResult.View.Render(viewContext, sw);
                viewResult.ViewEngine.ReleaseView(ControllerContext, viewResult.View);
                Logger.Trace("Leaving RenderRazorViewToString");
                return sw.GetStringBuilder().ToString();
            }
        }

        private void SetFooter(PdfDocumentControl pdfDocument, string address, string phoneNumber)
        {
            pdfDocument.Footer.Enabled = true;
            pdfDocument.Footer.Height = 25;
            //pdfDocument.Footer.BackgroundColor = System.Drawing.Color.WhiteSmoke;

            var footerHtmlContent = $@"
                <table width='100%' style='font-size: 16px; color: gray; font-family: Arial, sans-serif;'>
                    <tr>
                        <td style='text-align: center; width: 30%;'>{address}</td>
                        <td style='text-align: center; width: 20%;'>{phoneNumber}</td>
                        <td style='text-align: center; width: 25%;'>ecu.com</td>
                        <td style='text-align: center; width: 25%;'>Insured by NCUA</td>
                    </tr>
                </table>";




            PdfHtml footerHtml = new PdfHtml(0,0,footerHtmlContent, null);
            footerHtml.FitDestHeight = true;
            pdfDocument.Footer.Layout(footerHtml);
            //string depPath = @"C:\Orb\WebClient\Areas\ECUDirectDepositInfo\bin\HiQPdf.dep";
            string depPath = Path.Combine(System.Web.HttpContext.Current.Server.MapPath(@"~/Areas/ECUDirectDepositInfo/bin"), "HiQPdf.dep");
            try
            {
                footerHtml.SetDepFilePath(depPath);
            }
            catch(Exception ex)
            {
               
            }
        }
    }
}
