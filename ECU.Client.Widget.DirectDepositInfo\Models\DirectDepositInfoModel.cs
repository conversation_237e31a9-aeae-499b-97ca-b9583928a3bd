﻿using Alkami.Client.Framework.Mvc;
using System.Collections.Generic;

namespace ECU.Client.Widget.DirectDepositInfo.Models
{
    public class DirectDepositInfoModel : BaseModel
    {
        public DirectDepositInfoModel()
        {
            Accounts = new List<AccountInfoModel>();
        }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MemberNumber { get; set; }

        /// <summary>
        /// Gets or sets the accounts.
        /// </summary>
        public List<AccountInfoModel> Accounts { get; set; }
    }
}
