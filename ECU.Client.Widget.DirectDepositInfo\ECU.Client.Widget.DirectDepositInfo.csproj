﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Alkami.MicroServices.Settings.ProviderBasedClient.6.0.1\build\net472\Alkami.MicroServices.Settings.ProviderBasedClient.props" Condition="Exists('..\packages\Alkami.MicroServices.Settings.ProviderBasedClient.6.0.1\build\net472\Alkami.MicroServices.Settings.ProviderBasedClient.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{908CAC49-D0B7-40E6-88B3-C32DCE7A0764}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ECU.Client.Widget.DirectDepositInfo</RootNamespace>
    <AssemblyName>ECU.Client.Widget.DirectDepositInfo</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Alkami.Api.Common, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Api.Common.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Bank.Common, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Bank.Common.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Bank.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Bank.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Common, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Common.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Common.Utility, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Common.Utility.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Content.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Content.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Content.Data, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Content.Data.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Core.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Core.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Core.Data, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Core.Data.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.DecisionEngine, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.DecisionEngine.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.DecisionEngine.FactSheets, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.DecisionEngine.FactSheets.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.MessageCenter.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.MessageCenter.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Nag, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Nag.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Nag.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Nag.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Nag.Data, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Nag.Data.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Nag.Provider.InsufficientFundsAlerts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Nag.Provider.InsufficientFundsAlerts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Notification, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Notification.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Notification.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Notification.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Notification.Data, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Notification.Data.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.AddressValidation.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.AddressValidation.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.Core.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.Core.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.eStatements.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.eStatements.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.Risk.Alkami, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.Risk.Alkami.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.Risk.Shared, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.Risk.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.Shared, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.Shared.PSCU, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.Shared.PSCU.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Providers.StopPay.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Providers.StopPay.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Radium.Jobs, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Radium.Jobs.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Scheduler.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Scheduler.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.App.Tenant.Data, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.App.Tenant.Data.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.BillingRecord.Models, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.BillingRecord.Models.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Broker, Version=3.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Broker.3.0.1\lib\net462\Alkami.Broker.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Cache, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Cache.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Common, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Common.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Framework, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Management.Bank, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Management.Bank.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Messages, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Messages.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Audit, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Audit.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Bank, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Bank.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Content, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Content.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Core, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Core.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Exception, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Exception.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.MessageCenter, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.MessageCenter.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Nag, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Nag.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.Services.Security, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.Services.Security.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Client.WebClient.Shared, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Client.2024.2.0.957\lib\net472\Alkami.Client.WebClient.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Common, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Common.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Contracts, Version=4.9.1.918, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Core.4.9.1\lib\net462\Alkami.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Data.Access, Version=4.9.1.918, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Core.4.9.1\lib\net462\Alkami.Data.Access.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Data.Validations, Version=4.9.1.918, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Core.4.9.1\lib\net462\Alkami.Data.Validations.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Exceptions, Version=4.9.1.918, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Core.4.9.1\lib\net462\Alkami.Exceptions.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.ExternalMessaging, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.ExternalMessaging.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Ioc, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Ioc.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Accounts.Contracts.2.52, Version=2.52.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Accounts.Contracts.2.52.0\lib\net472\Alkami.MicroServices.Accounts.Contracts.2.52.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Accounts.Data.2.52, Version=2.52.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Accounts.Contracts.2.52.0\lib\net472\Alkami.MicroServices.Accounts.Data.2.52.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Accounts.Service.Client.2.52, Version=2.52.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Accounts.Service.Client.2.52.0\lib\net472\Alkami.MicroServices.Accounts.Service.Client.2.52.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Security.Contracts.2.34, Version=2.34.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Security.Contracts.2.34.0\lib\net472\Alkami.MicroServices.Security.Contracts.2.34.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Security.Data.2.34, Version=2.34.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Security.Contracts.2.34.0\lib\net472\Alkami.MicroServices.Security.Data.2.34.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Security.Data.Validations.2.34, Version=2.34.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Security.Contracts.2.34.0\lib\net472\Alkami.MicroServices.Security.Data.Validations.2.34.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Security.Service.Client.2.34, Version=2.34.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Security.Client.2.34.0\lib\net472\Alkami.MicroServices.Security.Service.Client.2.34.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Settings.Contracts.5.1, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Settings.Contracts.5.1.1\lib\net472\Alkami.MicroServices.Settings.Contracts.5.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Alkami.MicroServices.Settings.Data.5.1, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Settings.Contracts.5.1.1\lib\net472\Alkami.MicroServices.Settings.Data.5.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Alkami.MicroServices.Settings.Data.Validations.5.1, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Settings.Contracts.5.1.1\lib\net472\Alkami.MicroServices.Settings.Data.Validations.5.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Alkami.MicroServices.Settings.ProviderBased.Contracts, Version=6.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Settings.ProviderBased.Contracts.6.0.1\lib\net472\Alkami.MicroServices.Settings.ProviderBased.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Settings.ProviderBasedClient, Version=6.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Settings.ProviderBasedClient.6.0.1\lib\net472\Alkami.MicroServices.Settings.ProviderBasedClient.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.Settings.Service.Client.5.1, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Settings.Client.5.1.1\lib\net472\Alkami.MicroServices.Settings.Service.Client.5.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Alkami.MicroServices.SymConnectMultiplexer.Contracts.2.16, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.SymConnectMultiplexer.Contracts.2.16.0\lib\net472\Alkami.MicroServices.SymConnectMultiplexer.Contracts.2.16.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.SymConnectMultiplexer.Data.2.16, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.SymConnectMultiplexer.Contracts.2.16.0\lib\net472\Alkami.MicroServices.SymConnectMultiplexer.Data.2.16.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.SymConnectMultiplexer.Service.Client.2.16, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.SymConnectMultiplexer.Client.2.16.0\lib\net472\Alkami.MicroServices.SymConnectMultiplexer.Service.Client.2.16.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.MicroServices.SymConnectMultiplexer.Utilities.2.16, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.SymConnectMultiplexer.Utilities.2.16.0\lib\net472\Alkami.MicroServices.SymConnectMultiplexer.Utilities.2.16.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Monitoring, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Monitoring.2.11.0\lib\net462\Alkami.Monitoring.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security, Version=4.9.1.918, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.MicroServices.Core.4.9.1\lib\net462\Alkami.Security.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Common, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Common.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Common.Service, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Common.Service.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.IP-STS.Entrust, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.IP-STS.Entrust.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Provider, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Provider.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Provider.Shared.Entrust, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Provider.Shared.Entrust.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Provider.TokenTranslator, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Provider.TokenTranslator.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Provider.User.Entrust, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Provider.User.Entrust.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.RP-STS.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.RP-STS.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.SecurityManagement.Contracts, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.SecurityManagement.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Security.Web, Version=2024.2.0.957, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Common.2024.2.0.957\lib\net472\Alkami.Security.Web.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Services.Subscriptions.Data, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Subscription.Base.5.4.0\lib\net472\Alkami.Services.Subscriptions.Data.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Services.Subscriptions.ParticipatingClient, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Subscription.ParticipatingClient.5.4.0\lib\net472\Alkami.Services.Subscriptions.ParticipatingClient.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Services.Subscriptions.Resolver, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Subscription.Base.5.4.0\lib\net472\Alkami.Services.Subscriptions.Resolver.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Utilities, Version=2.3.0.943, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Utilities.2.3.0\lib\net462\Alkami.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Utilities.Certificates, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Utilities.2.3.0\lib\net462\Alkami.Utilities.Certificates.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Utilities.Configuration, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Utilities.Configuration.2.1.0\lib\net462\Alkami.Utilities.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Utilities.Kubernetes, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Utilities.Kubernetes.2.0.1\lib\net472\Alkami.Utilities.Kubernetes.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Utilities.LegacyIdentity, Version=1.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Utilities.LegacyIdentity.1.0.3\lib\net472\Alkami.Utilities.LegacyIdentity.dll</HintPath>
    </Reference>
    <Reference Include="Alkami.Utilities.Rpc, Version=5.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.Utilities.Rpc.5.0.1\lib\net472\Alkami.Utilities.Rpc.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AsyncIO, Version=0.1.69.0, Culture=neutral, PublicKeyToken=44a94435bd6f33f8, processorArchitecture=MSIL">
      <HintPath>..\packages\AsyncIO.0.1.69\lib\net40\AsyncIO.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=*******, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.5.1.1\lib\net462\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging, Version=3.4.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.3.4.1\lib\net40\Common.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging.Core, Version=3.4.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.Core.3.4.1\lib\net40\Common.Logging.Core.dll</HintPath>
    </Reference>
    <Reference Include="HiQPdf, Version=10.17.0.0, Culture=neutral, PublicKeyToken=acd8b62594985b24, processorArchitecture=MSIL">
      <HintPath>..\packages\hiqpdf_x64.10.17.0\lib\net40\HiQPdf.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="NaCl, Version=0.1.13.0, Culture=neutral, PublicKeyToken=827c20e50a9775fa, processorArchitecture=MSIL">
      <HintPath>..\packages\NaCl.Net.0.1.13\lib\net472\NaCl.dll</HintPath>
    </Reference>
    <Reference Include="NetMQ, Version=3.3.3.4, Culture=neutral, PublicKeyToken=a6decef4ddc58b3a, processorArchitecture=MSIL">
      <HintPath>..\packages\NetMQ.3.3.3.4\lib\net40\NetMQ.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.8.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=5.3.0.0, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.5.3.3\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.7.0.0\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reactive.Core, Version=2.2.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Rx-Core.2.2.5\lib\net45\System.Reactive.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Reactive.Interfaces, Version=2.2.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Rx-Interfaces.2.2.5\lib\net45\System.Reactive.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="System.Reactive.Linq, Version=2.2.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Rx-Linq.2.2.5\lib\net45\System.Reactive.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Reactive.PlatformServices, Version=2.2.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Rx-PlatformServices.2.2.5\lib\net45\System.Reactive.PlatformServices.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.7.0.0\lib\net462\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="mscorlib" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebToolkit, Version=4.1.2.1846, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Alkami.WebToolkit.4.1.2\lib\net462\WebToolkit.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <None Include="ECU.Client.Widget.DirectDepositInfo.nuspec" />
    <None Include="Tools\PostBuild.ps1" />
    <Content Include="Images\eculogo.jpg" />
    <Content Include="Images\ECUMarkClear.png" />
    <Content Include="Images\ecumoegray.jpg" />
    <Content Include="Images\ECU_Standard-Black White-RGB.png" />
    <Content Include="Styles\ECUDirectDepositInfo.css" />
    <Content Include="Views\ECUDirectDepositInfo\PrintPdf.cshtml" />
    <Content Include="Views\ECUDirectDepositInfo\Mobile\PrintPdf.cshtml" />
    <Compile Include="Controllers\BaseDirectDepositInfoController.cs" />
    <Compile Include="Models\AccountPropertyDetails.cs" />
    <Compile Include="Models\DirectDepositInfoModel.cs" />
    <Compile Include="Models\PrintPdfModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utils\SettingsUtil.cs" />
    <Compile Include="WidgetDescription.cs" />
    <Compile Include="Models\AccountInfoModel.cs" />
    <Compile Include="Controllers\MobileECUDirectDepositInfoController.cs" />
    <Compile Include="Controllers\ECUDirectDepositInfoController.cs" />
    <Content Include="AlkamiManifest.xml" />
    <Content Include="_SiteText\ECU.Client.Widget.DirectDepositInfo.sitetext.en.xml" />
    <Content Include="Views\ECUDirectDepositInfo\Mobile\Error.cshtml" />
    <Content Include="Views\ECUDirectDepositInfo\Mobile\Index.cshtml" />
    <Content Include="Views\ECUDirectDepositInfo\Error.cshtml" />
    <Content Include="Views\ECUDirectDepositInfo\Index.cshtml" />
    <Content Include="packages.config" />
    <None Include="..\sem.ver">
      <Link>sem.ver</Link>
    </None>
    <None Include="app.config" />
    <None Include="Tools\chocolateyInstall.ps1" />
    <None Include="Tools\chocolateyUninstall.ps1" />
    <None Include="Tools\install_widget.sql" />
    <None Include="Doc\GettingStarted.html" />
    <None Include="Doc\gettingstarted.css" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Scripts\" />
  </ItemGroup>
  <PropertyGroup>
  </PropertyGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>
if EXIST "$(ProjectDir)tools\PostBuild.ps1" (powershell.exe -NonInteractive -command "$(ProjectDir)tools\PostBuild.ps1" '$(ProjectName)' '$(ProjectDir)')</PostBuildEvent>
  </PropertyGroup>
  <Import Project="..\packages\RazorGenerator.MsBuild.2.5.0\build\RazorGenerator.MsBuild.targets" Condition="Exists('..\packages\RazorGenerator.MsBuild.2.5.0\build\RazorGenerator.MsBuild.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\RazorGenerator.MsBuild.2.5.0\build\RazorGenerator.MsBuild.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\RazorGenerator.MsBuild.2.5.0\build\RazorGenerator.MsBuild.targets'))" />
    <Error Condition="!Exists('..\packages\Alkami.MicroServices.Settings.ProviderBasedClient.6.0.1\build\net472\Alkami.MicroServices.Settings.ProviderBasedClient.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Alkami.MicroServices.Settings.ProviderBasedClient.6.0.1\build\net472\Alkami.MicroServices.Settings.ProviderBasedClient.props'))" />
  </Target>
  <PropertyGroup>
    <PreBuildEvent>if EXIST "$(ProjectDir)\obj\CodeGen\" (rmdir /s /q "$(ProjectDir)\obj\CodeGen\")</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>