﻿@model ECU.Client.Widget.DirectDepositInfo.Models.DirectDepositInfoModel

@using Alkami.Client.WebClient.Shared.Helpers

@section TitleContentPlaceholder{
    @Html.SiteText("Title")
}

@section StyleSheetContentPlaceholder{
    <link rel="stylesheet" type="text/css" href="~/Areas/ECUDirectDepositInfo/Styles/ECUDirectDepositInfo.css" />
}


<div>
    Need to setup a direct deposit or withdrawal to your Educators account? Select the share you want to use from the drop down below and hit submit to download
    a Direct Deposit form. This PDF contains all the information needed to complete your employers direct deposit form or setup bills online to be paid by ACH.
</div>
<br />
<div>
    When setting up a new deposit or withdrawal always double check you are generating and providing the right account number.
</div>
<br />
@using (Html.BeginForm("GetPdf", "ECUDirectDepositInfo", FormMethod.Get, new { id = "getPdfForm" }))
{
    <div class="iris-dropdown" id="account-dropdown" name="documentId" placeholder="Select an account" data-size="large">
        <ul class="iris-options-list">
            @if (Model.Accounts.Count > 0)
            {
                foreach (var account in @Model.Accounts)
                {
                    <li class="iris-option" data-value="@account.AccountIdentifier" data-color="account-color-@(account.ThemeColorIndex % 8 )">
                        <div class="iris-account iris-account--multi-line" data-color="account-color-@(account.ThemeColorIndex % 8 )">
                            <span class="iris-account__color-bar"></span>
                            <div class="iris-account__column iris-account__account-details">
                                <div class="iris-account__info iris-account__primary-info">
                                    <span class="iris-account__account-name" title="@account.DisplayName">@account.DisplayName</span>
                                </div>
                                <div class="iris-account__info iris-account__secondary-info">
                                    <span class="iris-account__account-number">
                                        <span role="text" aria-label="Account number ending in">@account.AccountNumber</span>
                                        @if (account.IsLinkedAccount)
                                        {
                                            <span class="font-icon-linked" role="img" aria-label="Shared Account"></span>
                                        }
                                    </span>
                                </div>
                            </div>
                            <div class="iris-account-balance iris-account-balance--available-balance">
                                <div class="iris-account-balance__row">
                                    <span class="iris-account-balance__icon available-balance-icon" role="img" aria-label="Available balance"></span>
                                    <span class="iris-account-balance__primary-info">@string.Format("{0:C}", @account.AccountBalance)</span>
                                </div>
                            </div>
                        </div>
                    </li>
                }
            }
        </ul>
    </div>
    <br />
    <button class="iris-button iris-button--primary" id="submitButton" disabled="disabled">
        <span class="iris-button__text">Generate PDF</span>
    </button>
}

<script type="text/javascript">

    var element = document.getElementById('account-dropdown');

    element.addEventListener('iris.dropdown.change', function (e) {
        $('#submitButton').prop('disabled', false);
    });

    var form = document.getElementById('getPdfForm');
    form.addEventListener('submit', function (e) {
        e.preventDefault();
        var dropdownComponent = Alkami.Iris.DropdownComponent.componentForElement(element);

        window.open('@Url.Action("GetPdf", this.ViewContext.RouteData.Values["controller"].ToString())?documentId=' + dropdownComponent.value, '_blank');
    });
</script>