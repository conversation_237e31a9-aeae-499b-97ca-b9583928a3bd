﻿using Alkami.Client.Framework.Mvc;
using System;
using System.Collections.Generic;

namespace ECU.Client.Widget.DirectDepositInfo.Models
{
    public class AccountInfoModel : BaseModel
    {
		public AccountInfoModel()
		{
			ExtendedProperties = new Dictionary<string, string>();
		}

		public string DisplayName { get; set; }
		public string MobileDisplayName { get; set; }
		public string AccountType { get; set; }
		public string AccountNumber { get; set; }
		public decimal? AccountBalance { get; set; }

		public Dictionary<string, string> ExtendedProperties { get; set; }
		public AccountPropertyDetails AccountDetails { get; set; }
		public Dictionary<string, string> AccountProperties { get; set; }

		public int? ThemeColorIndex { get; set; }
		public string AccountIdentifier { get; set; }
		public bool IsLinkedAccount { get; set; }
	}
}
