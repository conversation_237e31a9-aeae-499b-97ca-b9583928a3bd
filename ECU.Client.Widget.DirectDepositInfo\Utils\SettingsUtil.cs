﻿using System;
using System.Collections.Generic;
using Alkami.Client.Framework.Utility;
using Alkami.Client.Services.Core.Models;

namespace ECU.Client.Widget.DirectDepositInfo.Utils
{
    public static class SettingsUtil
    {
        private const string HIQPDF_SERIAL = "hiqpdf_serial";
        private const string PHONE_NUMBER = "phone_number";
        private const string CREATE_ACCOUNT_NOTE = "create_account_note";
        private const string CREATE_NOTE_POWERON = "create_note_poweron";
        private const string ECU_ADDRESS = "ecu_address";
        public static string HiQPDFSerial(this ICollection<UserWidgetSetting> settings)
        {
            return WidgetSettingsUtil.GetStringValue(settings, HIQPDF_SERIAL);
        }

        public static string PhoneNumber(this ICollection<UserWidgetSetting> settings)
        {
            return WidgetSettingsUtil.GetStringValue(settings,  PHONE_NUMBER);
        }

        public static string ECUAddress(this ICollection<UserWidgetSetting> settings)
        {
            return WidgetSettingsUtil.GetStringValue(settings, ECU_ADDRESS);
        }

        public static string CreateAccountNote(this ICollection<UserWidgetSetting> settings)
        {
            return WidgetSettingsUtil.GetStringValue(settings, CREATE_ACCOUNT_NOTE);
        }

        public static string CreateNotePoweron(this ICollection<UserWidgetSetting> settings)
        {
            return WidgetSettingsUtil.GetStringValue(settings, CREATE_NOTE_POWERON);
        }
    }
}
