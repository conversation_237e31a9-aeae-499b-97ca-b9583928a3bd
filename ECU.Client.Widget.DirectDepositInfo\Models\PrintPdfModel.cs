﻿using Alkami.Client.Framework.Mvc;

namespace ECU.Client.Widget.DirectDepositInfo.Models
{
    public class PrintPdfModel : BaseModel
    {
        public string MICR { get; set; }
        public string RoutingNumber { get; set; }
        public string AccountType { get; set; }
        public string CreditUnionAddress { get; set; }
        public string Name { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string CityStateZip { get; set; }
        public string PDFHeaderLogo { get; set; }
        public string CheckBackgroundImage { get; set; }
        public string CheckECULogo { get; set; }
        public string PhoneNumber { get; set; }
        public string ECUAddress { get; set; }
    }
}