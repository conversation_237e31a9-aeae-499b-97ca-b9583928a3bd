﻿@model ECU.Client.Widget.DirectDepositInfo.Models.PrintPdfModel
@using Alkami.Client.WebClient.Shared.Helpers
@section TitleContentPlaceholder{
    @Html.SiteText("MobileTitle")
}

@section StyleSheetContentPlaceholder{
    <link rel="stylesheet" type="text/css" href="~/Areas/ECUDirectDepositInfo/Styles/ECUDirectDepositInfo.css" />
}
<style>
    .pdf-body {
        font-size: 28px;
        font-family: Calibri;
        padding-bottom: 30px;
    }

    .text-center {
        text-align: center;
    }

    .font-weight-bold {
        font-weight: bold;
    }

    .hidden {
        visibility: hidden;
    }

    #ecu-name {
        text-transform: capitalize;
    }


    .voided-check-container {
        opacity: 1 !important;
        z-index: auto !important;
        display: flex;
        border: 2px solid black;
        background-image: url(@Model.CheckBackgroundImage);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        height: auto;
        flex-direction: column;
        position: relative;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

.voided-check-container img{
    width:300px;
    height:auto;
}

.header-details {
    display: flex;
    padding: 10px;
}

.date-section {
    text-align: right;
    position: relative;
    border-bottom: 1px solid black;
    width: 25%;
}

.date-blank {
    width: 75%;
}

.details-container {
    display: flex;
    padding: 10px;
}

.order-of {
    flex: 3;
    text-align: left;
    border-bottom: 1px solid black;
    margin-right: 10px;
}

.money-amount {
    background-color: lightgray;
    flex: 1.5;
    text-align: left;
    font-size: 48px;
}

.dollars-container {
    display: flex;
    padding: 10px;
}

.dollars-line {
    width: 100%;
    position: relative;
    text-align: right;
    border-bottom: 1px solid black;
}

.image-container {
    display: flex;
    padding: 20px;
}

.img{
    width:25%;
    margin-right:10px;
}

.img img{
    width: 100%;
    height: auto;
}

.address-info {
    width:70%;
}

.address-info p{
    margin:0;
}

.memo-container{
    display: flex;
    padding-left: 100px;
    padding-right: 210px;

}

.memo{
    flex:2;
    border-bottom: 1px solid black;
    margin-right: 60px;
}

.blank-line{
    flex: 2;
    border-bottom: 1px solid black;
}

.account-container{
    display:flex;
    padding:30px;
    font-size:38px;
    color:gray;
}

.void-overlay {
    position: absolute;
    top: 25%;
    left: 10%;
    font-size: 72px;
    color: gray;
    font-weight: bold;
    transform: rotate(-15deg);
    opacity: 0.75;
}

.void-overlay-two {
    position: absolute;
    top: 40%;
    left: 13%;
    font-size: 72px;
    color: gray;
    font-weight: bold;
    transform: rotate(-15deg);
    opacity: 0.75;
}

.void-overlay-three {
    position: absolute;
    top: 55%;
    left: 15%;
    font-size: 72px;
    color: gray;
    font-weight: bold;
    transform: rotate(-15deg);
    opacity: 0.75;
}
</style>
<div class="pdf-body">
    <div class="pdf-header">
        <img src="@Model.PDFHeaderLogo" height="15%" />
    </div>
    <br />
    <br />
    <br />
    <br />
    <div>@DateTime.Now.Date.ToString("MM-dd-yyyy")</div>
    <br />
    <div>@Model.Name </div>

    <div>@Model.AddressLine1</div>
    @if (string.IsNullOrEmpty(Model.AddressLine2) == false)
    {
        <div>@Model.AddressLine2</div>
    }
    <div>@Model.CityStateZip</div>
    <br />
    <br />
    <br />
    <div>Dear <span id="ecu-name">@Model.Name.ToLower() </span></div>
    <br />
    <br />
    <div>
        Thank you for choosing Educators Credit Union for your financial needs! The purpose of this letter is to verify that your @Model.AccountType with Educators Credit Union is active.
        You will also find your account details to provide for direct deposit/automatic withdrawal purposes.
    </div>
    <br />
    <div class='text-center font-weight-bold'>Account Type: @Model.AccountType</div>
    <div class='text-center font-weight-bold'>Account Number: @Model.MICR</div>
    <div class='text-center font-weight-bold'>Routing Number: *********</div>
    <div class='text-center font-weight-bold'>Educators Credit Union Address: 1400 Newman Rd, Racine WI 53406</div>
    <br />
    <div>If you have additional questions, please visit www.ecu.com, contact us at @Model.PhoneNumber, or stop by any branch.</div>
    <br />
    <br />
    <div>Sincerely,</div>
    <br />
    <div>Educators Credit Union</div>
    <div class="hidden">...</div>
    @if (Model.AccountType == "Checking")
    {
        <div class="voided-check-container">
            <div class="header-details">
                <div class="date-blank"></div>
                <div class="date-section">date</div>
            </div>
            <div class="details-container">
                <div class="order-of">Pay to the<br /> order of</div>
                <div class="money-amount">$</div>
            </div>

            <div class="dollars-container">
                <div class="dollars-line">dollars</div>
            </div>

            <div class="image-container">
                <div class="img">
                    <img src="@Model.CheckECULogo" />
                </div>
                <div class="address-info">
                    <p>P.O Box 0810040</p>
                    <p>Racine, WI 53408</p>
                    <p>@Model.PhoneNumber</p>
                </div>
            </div>

            <div class="memo-container">
                <div class="memo">Memo</div>
                <div class="blank-line"></div>
            </div>

            <div class="account-container">
                <div>
                    <b>|:</b>*********<b>|:</b>     <b>||'</b>@Model.MICR<b>||'</b>
                </div>

            </div>

            <div class="void-overlay">***VOID***VOID***VOID***</div>
            <div class="void-overlay-two">***VOID***VOID***VOID***</div>
            <div class="void-overlay-three">***VOID***VOID***VOID***</div>
        </div>
    }
</div>