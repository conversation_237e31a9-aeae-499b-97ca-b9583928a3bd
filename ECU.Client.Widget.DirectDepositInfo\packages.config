﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Alkami.Broker" version="3.0.1" targetFramework="net48" />
  <package id="Alkami.Client" version="2024.2.0.957" targetFramework="net48" />
  <package id="Alkami.Common" version="2024.2.0.957" targetFramework="net48" />
  <package id="Alkami.Installer.Widget" version="1.0.13" targetFramework="net48" />
  <package id="Alkami.Installer.Widget.PostBuild" version="1.0.7" targetFramework="net48" />
  <package id="Alkami.MicroServices.Accounts.Contracts" version="2.52.0" targetFramework="net48" />
  <package id="Alkami.MicroServices.Accounts.Service.Client" version="2.52.0" targetFramework="net48" />
  <package id="Alkami.MicroServices.Core" version="4.9.1" targetFramework="net48" />
  <package id="Alkami.MicroServices.Security.Client" version="2.34.0" targetFramework="net48" />
  <package id="Alkami.MicroServices.Security.Contracts" version="2.34.0" targetFramework="net48" />
  <package id="Alkami.MicroServices.Settings.Client" version="5.1.1" targetFramework="net48" />
  <package id="Alkami.MicroServices.Settings.Contracts" version="5.1.1" targetFramework="net48" />
  <package id="Alkami.MicroServices.Settings.ProviderBased.Contracts" version="6.0.1" targetFramework="net48" />
  <package id="Alkami.MicroServices.Settings.ProviderBasedClient" version="6.0.1" targetFramework="net48" />
  <package id="Alkami.MicroServices.SymConnectMultiplexer.Client" version="2.16.0" targetFramework="net48" />
  <package id="Alkami.MicroServices.SymConnectMultiplexer.Contracts" version="2.16.0" targetFramework="net48" />
  <package id="Alkami.MicroServices.SymConnectMultiplexer.Utilities" version="2.16.0" targetFramework="net48" />
  <package id="Alkami.Monitoring" version="2.11.0" targetFramework="net48" />
  <package id="Alkami.Subscription.Base" version="5.4.0" targetFramework="net48" />
  <package id="Alkami.Subscription.ParticipatingClient" version="5.4.0" targetFramework="net48" />
  <package id="Alkami.Utilities" version="2.3.0" targetFramework="net48" />
  <package id="Alkami.Utilities.Certificates" version="1.1.1" targetFramework="net48" />
  <package id="Alkami.Utilities.Configuration" version="2.1.0" targetFramework="net48" />
  <package id="Alkami.Utilities.Kubernetes" version="2.0.1" targetFramework="net48" />
  <package id="Alkami.Utilities.LegacyIdentity" version="1.0.3" targetFramework="net48" />
  <package id="Alkami.Utilities.Rpc" version="5.0.1" targetFramework="net48" />
  <package id="Alkami.WebToolkit" version="4.1.2" targetFramework="net48" />
  <package id="Antlr" version="*******" targetFramework="net48" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net48" />
  <package id="AsyncIO" version="0.1.69" targetFramework="net48" />
  <package id="Castle.Core" version="5.1.1" targetFramework="net48" />
  <package id="Common.Logging" version="3.4.1" targetFramework="net48" />
  <package id="Common.Logging.Core" version="3.4.1" targetFramework="net48" />
  <package id="hiqpdf_x64" version="10.17.0" targetFramework="net48" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net48" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net472" />
  <package id="NaCl.Net" version="0.1.13" targetFramework="net48" />
  <package id="NetMQ" version="3.3.3.4" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="8.0.3" targetFramework="net48" />
  <package id="NHibernate" version="5.3.3" targetFramework="net48" />
  <package id="RazorGenerator.MsBuild" version="2.5.0" targetFramework="net48" developmentDependency="true" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net48" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net48" />
  <package id="Rx-Core" version="2.2.5" targetFramework="net48" />
  <package id="Rx-Interfaces" version="2.2.5" targetFramework="net48" />
  <package id="Rx-Linq" version="2.2.5" targetFramework="net48" />
  <package id="Rx-Main" version="2.2.5" targetFramework="net48" />
  <package id="Rx-PlatformServices" version="2.2.5" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Configuration.ConfigurationManager" version="7.0.0" targetFramework="net48" />
  <package id="System.Data.DataSetExtensions" version="4.5.0" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.AccessControl" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net48" />
  <package id="System.Security.Permissions" version="7.0.0" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="WebActivatorEx" version="2.2.0" targetFramework="net48" />
</packages>