<?xml version="1.0"?>
<package>
	<metadata>
		<id>ECU.Client.Widget.DirectDepositInfo</id>
		<version>1.2.0</version>
		<title>DirectDepositInfo</title>
		<authors><PERSON></authors>
		<owners>Alkami Technology, Inc.</owners>
		<projectUrl>https://www.ecu.com</projectUrl>
		<iconUrl>https://www.ecu.com</iconUrl>
		<licenseUrl>https://www.ecu.com</licenseUrl>
		<requireLicenseAcceptance>false</requireLicenseAcceptance>
		<description>ECU Direct Deposit Letter</description>
		<releaseNotes>Put any release notes you like here.</releaseNotes>
		<tags>SDK WIDGET</tags>
		<copyright>Copyright ©  2021</copyright>
		<dependencies>

		</dependencies>
	</metadata>
	<files>
		<!-- The following defines where to find the files for packaging and what to exclude-->
		<file src="tools\chocolateyInstall.ps1" target="tools" />
		<file src="tools\chocolateyUninstall.ps1" target="tools" />
		<file src="AlkamiManifest.xml" target="AlkamiManifest.xml" />
		<file src="**\*.*" target="src" exclude="**\obj\**\*.*;**\.vs\**\*.*;**\bin\**\*.*;**\packages\**\*.*;**\.nuget\**\*.*;**\.git\**\*.*;**\.gitignore;**\node_modules\**\*.*;**\.suo;**\.user;**\Tests\**\*.*;**\Test\**\*.*;**\UnitTest\**\*.*;**\UnitTests\**\*.*;**\tools\**\chocolatey*.ps1" />

		<!-- bin files | define the libraries that should be a part of this package -->
		<file src="bin\ECU.Client.Widget.DirectDepositInfo.*" target="lib" exclude="**\*.config"/>
		<file src="bin\Alkami.MicroServices.Accounts.*" target="lib" exclude="**\*.config"/>
		<file src="bin\Alkami.MicroServices.Security.*" target="lib" exclude="**\*.config"/>
		<file src="bin\Alkami.MicroServices.Settings.*" target="lib" exclude="**\*.config;bin\Alkami.MicroServices.Settings.ProviderBased.Contracts.dll;bin\Alkami.MicroServices.Settings.ProviderBasedClient.dll "/>
		<file src="bin\Alkami.MicroServices.SymConnectMultiplexer.*" target="lib" exclude="**\*.config"/>
		<file src="bin\HiQPdf.*" target="lib" exclude="**\*.config"/>

		<!-- WebClient content -->
		<file src="**\Scripts\" target="content\Areas\App" />
		<file src="**\Styles\" target="content\Areas\App" exclude="**\*.scss" />
		<file src="**\Views\" target="content\Areas\App" exclude="**\obj\**\*.*" />
		<file src="**\Images\" target="content\Areas\App" />
		<file src="**\_SiteText\" target="content\Areas\App" exclude="**\*.xx.xml"/>
	</files>
</package>
